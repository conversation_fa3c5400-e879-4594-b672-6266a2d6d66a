<?php

namespace App\Http\Controllers;

use App\Booking;
use App\Card;
use App\CommonSetting;
use App\Listing;
use App\ListingAccessibility;
use App\ListingAmenity;
use App\ListingDetail;
use App\ListingEquipment;
use App\ListingExperience;
use App\ListingExtra;
use App\listingGallery;
use App\ListingInclude;
use App\ListingItinerary;
use App\ListingKeyFeature;
use App\ListingService;
use App\Models\Cart;
use App\Models\ChMessage;
use App\Models\ContactInfo;
use App\Models\CurrencyConversionRate;
use App\Models\Notification;
use App\Models\ReservationDate;
use App\Models\ReviewImage;
use App\Models\ReviewReport;
use App\Models\User;
use App\Report;
use App\Wallet;
use App\WithdrawalRequest;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Stripe\Stripe;
use Stripe\Token;

class DashboardController extends Controller
{
    private $payerName = 'Luxustars'; // Replace with your actual payer name
    private $apiKey = 'WjEXzlzKcQR9mDJZxMdO/JxAZvd210j7pdUvwSxWcr3w9h7IK+eMk6H3iV0/6qjt'; // Replace with your actual API key
    private $clientId;
    private $clientSecret;
    public function __construct()
    {
        $this->clientId = "tipalti.thirdpartyapi.FTeCrli-8e3rUbeQTMrzBh4FxKg";
        $this->clientSecret = "aSTyuU6SY0rjOzx0EcVzOYqyW_0";
    }
    function index()
    {
        $bookings = Booking::where("status", 0)->get();
        $withdrawal_requests = WithdrawalRequest::where("status", 0)->get();

        // For service role users, filter bookings with proper logic
        $today_bookings = collect();
        $upcoming_bookings = collect();

        if (auth()->user()->hasRole('service')) {
            // Ensure we're using Colombia timezone consistently
            $today = \Carbon\Carbon::now('America/Bogota')->toDateString();
            $currentTime = \Carbon\Carbon::now('America/Bogota');

            // Get today's bookings - bookings that are happening today (ongoing, upcoming, completed, or cancelled)
            $today_bookings = Booking::whereIn("status", [0, 3, 7]) // Include pending, completed, and cancelled
                ->where("provider_id", auth()->id())
                ->where(function ($q) use ($today) {
                    // Include bookings that are ongoing today (check_in <= today AND check_out >= today)
                    $q->where(\DB::raw('DATE(check_in)'), '<=', $today)
                        ->where(\DB::raw('DATE(check_out)'), '>=', $today);
                })
                ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots'])
                ->get()
                ->sort(function ($a, $b) use ($today, $currentTime) {
                    // Helper function to determine booking priority
                    $getPriority = function ($booking) use ($today, $currentTime) {
                        $checkInDate = \Carbon\Carbon::parse($booking->check_in)->setTimezone('America/Bogota')->toDateString();
                        $checkOutDate = \Carbon\Carbon::parse($booking->check_out)->setTimezone('America/Bogota')->toDateString();

                        // Priority: 1 = On Going, 2 = Upcoming, 3 = Completed, 4 = Cancelled

                        // First check booking status
                        if ($booking->status == 7) {
                            return 4; // Cancelled priority (lowest)
                        }

                        if ($booking->status == 3) {
                            return 3; // Completed priority
                        }

                        // For pending bookings (status 0), determine if ongoing or upcoming
                        if ($booking->status == 0) {
                            // Check if this is an ongoing booking (started before or today, ends today or later)
                            if ($checkInDate <= $today && $checkOutDate >= $today) {
                                // For Daily bookings, this is "On Going"
                                if ($booking->listing_basis != 'Hourly') {
                                    return 1; // On Going priority
                                } else {
                                    // For Hourly bookings, check if current time is within any booked slot
                                    $hourlySlots = $booking->hourly_slots;
                                    if ($hourlySlots && $hourlySlots->count() > 0) {
                                        $allSlotsPassed = true;

                                        foreach ($hourlySlots as $slot) {
                                            $slotParts = explode(' - ', $slot->slot);
                                            if (count($slotParts) == 2) {
                                                $slotStartTime = \Carbon\Carbon::parse($slot->date . ' ' . $slotParts[0])->setTimezone('America/Bogota');
                                                $slotEndTime = \Carbon\Carbon::parse($slot->date . ' ' . $slotParts[1])->setTimezone('America/Bogota');

                                                // Handle midnight crossover
                                                if ($slotEndTime <= $slotStartTime) {
                                                    $slotEndTime->addDay();
                                                }

                                                // Check if current time is within this slot
                                                if ($currentTime->between($slotStartTime, $slotEndTime)) {
                                                    return 1; // On Going priority
                                                }

                                                // If any slot's end time is in the future, not all slots have passed
                                                if ($slotEndTime > $currentTime) {
                                                    $allSlotsPassed = false;
                                                }
                                            }
                                        }

                                        // If all slots have passed, it should be completed but status is still 0
                                        if ($allSlotsPassed) {
                                            return 3; // Completed priority
                                        } else {
                                            return 1; // On Going priority (has future slots)
                                        }
                                    }
                                }
                            }

                            // Check if this is an upcoming booking (starts today but in the future)
                            if ($checkInDate == $today) {
                                return 2; // Upcoming priority
                            }
                        }

                        // Default to completed for other cases
                        return 3; // Completed priority
                    };

                    $priorityA = $getPriority($a);
                    $priorityB = $getPriority($b);

                    // First sort by priority
                    if ($priorityA != $priorityB) {
                        return $priorityA <=> $priorityB;
                    }

                    // If same priority, sort by check-in date (nearest dates first)
                    $checkInA = \Carbon\Carbon::parse($a->check_in)->setTimezone('America/Bogota');
                    $checkInB = \Carbon\Carbon::parse($b->check_in)->setTimezone('America/Bogota');

                    return $checkInA <=> $checkInB;
                })
                ->values(); // Reset array keys after sorting

            // Get upcoming bookings - bookings that start in the future (not today)
            $upcoming_bookings = Booking::where("status", 0)
                ->where("provider_id", auth()->id())
                ->where(function ($q) use ($today, $currentTime) {
                    // Include future date bookings
                    $q->where('check_in', '>', $today)
                        ->orWhere(function ($q2) use ($today, $currentTime) {
                            // Include same-day hourly bookings with future slots only
                            $q2->where(\DB::raw('DATE(check_in)'), '=', $today)
                                ->where('listing_basis', 'Hourly')
                                ->whereHas('hourly_slots', function ($q3) use ($currentTime) {
                                    // Has at least one future slot
                                    $q3->whereRaw("
                                     CASE
                                         WHEN TIME(SUBSTRING_INDEX(slot, ' - ', 1)) <= TIME(SUBSTRING_INDEX(slot, ' - ', -1))
                                         THEN CONCAT(date, ' ', SUBSTRING_INDEX(slot, ' - ', 1))
                                         ELSE CONCAT(date, ' ', SUBSTRING_INDEX(slot, ' - ', 1))
                                     END > ?
                                 ", [$currentTime]);
                                })
                                // Exclude if currently ongoing
                                ->whereDoesntHave('hourly_slots', function ($q4) use ($currentTime) {
                                    $q4->whereRaw("
                                     TIME(?) BETWEEN
                                     TIME(SUBSTRING_INDEX(slot, ' - ', 1)) AND
                                     TIME(SUBSTRING_INDEX(slot, ' - ', -1))
                                 ", [$currentTime->format('H:i:s')]);
                                });
                        });
                })
                ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots'])
                ->get();

            // Get tomorrow's bookings - bookings that are either starting tomorrow OR ongoing tomorrow
            $tomorrow = \Carbon\Carbon::now('America/Bogota')->addDay()->toDateString();
            $tomorrow_bookings = Booking::whereIn("status", [0, 3, 7]) // Include pending, completed, and cancelled
                ->where("provider_id", auth()->id())
                ->where(function ($q) use ($tomorrow, $today) {
                    // Include bookings that start tomorrow
                    $q->where(\DB::raw('DATE(check_in)'), '=', $tomorrow)
                        // OR include ongoing bookings that are still ongoing tomorrow (checkout >= tomorrow)
                        ->orWhere(function ($q2) use ($tomorrow, $today) {
                            $q2->where(\DB::raw('DATE(check_in)'), '<=', $today)
                                ->where(\DB::raw('DATE(check_out)'), '>=', $tomorrow);
                        });
                })
                ->with(['customer', 'listing', 'listing.category', 'statusName', 'hourly_slots'])
                ->get()
                ->sort(function ($a, $b) use ($today, $tomorrow, $currentTime) {
                    // Helper function to determine booking priority for tomorrow tab
                    $getPriority = function ($booking) use ($today, $tomorrow, $currentTime) {
                        $checkInDate = \Carbon\Carbon::parse($booking->check_in)->setTimezone('America/Bogota')->toDateString();
                        $checkOutDate = \Carbon\Carbon::parse($booking->check_out)->setTimezone('America/Bogota')->toDateString();

                        // Priority: 1 = On Going, 2 = Upcoming, 3 = Completed, 4 = Cancelled

                        // First check booking status
                        if ($booking->status == 7) {
                            return 4; // Cancelled priority (lowest)
                        }

                        if ($booking->status == 3) {
                            return 3; // Completed priority
                        }

                        // For pending bookings (status 0), determine if ongoing or upcoming
                        if ($booking->status == 0) {
                            // Check if this is an ongoing booking that spans tomorrow
                            if ($checkInDate <= $today && $checkOutDate >= $tomorrow) {
                                return 1; // On Going priority (ongoing tomorrow)
                            }

                            // Check if this is a booking starting tomorrow
                            if ($checkInDate == $tomorrow) {
                                return 2; // Upcoming priority
                            }
                        }

                        // Default to completed for other cases
                        return 3; // Completed priority
                    };

                    $priorityA = $getPriority($a);
                    $priorityB = $getPriority($b);

                    // First sort by priority
                    if ($priorityA != $priorityB) {
                        return $priorityA <=> $priorityB;
                    }

                    // If same priority, sort by check-in date (nearest dates first)
                    $checkInA = \Carbon\Carbon::parse($a->check_in)->setTimezone('America/Bogota');
                    $checkInB = \Carbon\Carbon::parse($b->check_in)->setTimezone('America/Bogota');

                    return $checkInA <=> $checkInB;
                })
                ->values(); // Reset array keys after sorting
        } else {
            $tomorrow_bookings = collect(); // Empty collection for non-service users
        }

        return view('dashboard.index', compact("bookings", "withdrawal_requests", "today_bookings", "upcoming_bookings", "tomorrow_bookings"));
    }
    function dashboard_inbox()
    {
        return view("dashboard.chat");
    }
    public function contactMessage()
    {
        $contact_infos = ContactInfo::latest()->get();
        return view('dashboard.contact', compact("contact_infos"));
    }
    public function listingCmsIndex()
    {
        return view('listingCMS.index');
    }
    public function listingCmsCreateAcc()
    {
        return view('listingCMS.edit');
    }
    public function reportsResolved($id)
    {
        $reports = Report::findOrFail($id);
        $reports->status = 1;
        $reports->save();
        return back()->with('flash_message', 'Report Resolved!');
    }
    public function checkIsPayable(Request $request)
    {
        $user = auth()->user();
        return response()->json([
            'is_payable' => $user->is_payable
        ]);
    }
    public function eWallet()
    {
        $wallet_amount = 0;
        $wallet = Wallet::where("user_id", auth()->id())->first();
        if ($wallet) {
            $wallet_amount = $wallet->amount ?? 0;
        }
        $booking = new Booking();
        $withdraw = new WithdrawalRequest();
        $usd_conversion_rate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate') ?? 1;  // Default to 1 if null
        $wallet_usd_amount = number_format($this->currency('COP', $wallet_amount), 2);
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        $earning_month = $booking->where('provider_id', auth()->id())->whereBetween('created_at', [$startDate, $endDate])->sum('total_amount');
        $total_earning = $withdraw->where('user_id', auth()->id())
            ->where('status', 4)
            ->sum('amount');
        $total_earning_usd = $total_earning * $usd_conversion_rate;
        // $pending_withdrawal = $booking->where('provider_id', auth()->id())
        //     ->where('is_wallet', 0)
        //     ->sum('sub_total');
        $pending_withdrawal = $booking->where('provider_id', auth()->id())
            ->where('is_wallet', 0)
            ->where("status", "!=", 7)
            ->get()
            ->sum(function ($booking) {
                // Calculate amount after deducting commission, similar to CronController
                return $booking->sub_total - get_percentage($booking->listing->category->tax ?? 0, $booking->sub_total);
            });
        $pending_withdrawal_usd = $pending_withdrawal * $usd_conversion_rate;
        $in_process = $withdraw->where('user_id', auth()->id())
            ->where('status', 0)
            ->sum('amount');
        $in_process_usd = $in_process * $usd_conversion_rate;
        $total_cancellation = $booking->where('provider_id', auth()->id())
            ->where('status', 7)
            ->where('is_wallet', 0)
            ->sum('sub_total');
        $total_cancellation_usd = $total_cancellation * $usd_conversion_rate;
        $adjustments = $booking->where('provider_id', auth()->id())
            ->where('status', 7)
            ->where('is_wallet', 1)
            ->sum('service_refund_amount_cop');
        $adjustments_usd = $adjustments * $usd_conversion_rate;
        $iframeUrl = $this->generateIframeUrl(auth()->user()->ids ?? '');
        $commission_amount = $booking->where('status', 0)->sum('commission_amount');
        $withdrawal_requests = $withdraw->where("user_id", auth()->id())->orderBy('created_at', 'desc')->get();
        return view('dashboard.e_wallet', compact(
            'wallet',
            'iframeUrl',
            'commission_amount',
            'earning_month',
            'total_earning',
            'total_earning_usd',
            'pending_withdrawal',
            'pending_withdrawal_usd',
            'in_process',
            'in_process_usd',
            'total_cancellation',
            'total_cancellation_usd',
            'adjustments',
            'adjustments_usd',
            'wallet_usd_amount',
            'usd_conversion_rate',
            'withdrawal_requests'
        ));
    }
    public function getEarningData(Request $request)
    {
        $filter = $request->get('filter');  // No default
        $startDate = null;
        $endDate = null;
        $applyDateFilter = false;
        if ($filter) {
            switch ($filter) {
                case 'today':
                    $startDate = Carbon::today()->startOfDay();
                    $endDate = Carbon::today()->endOfDay();
                    break;
                case 'this_week':
                    $startDate = Carbon::now()->startOfWeek();
                    $endDate = Carbon::now()->endOfWeek();
                    break;
                case 'last_month':
                    $startDate = Carbon::now()->subMonth()->startOfMonth();
                    $endDate = Carbon::now()->subMonth()->endOfMonth();
                    break;
                case 'last_year':
                    $startDate = Carbon::now()->subYear()->startOfYear();
                    $endDate = Carbon::now()->subYear()->endOfYear();
                    break;
                case 'lifetime':
                    // No date filter for lifetime - get all data
                    $applyDateFilter = false;
                    break;
            }
            if ($startDate && $endDate) {
                $applyDateFilter = true;
            }
        }
        $userId = auth()->id();
        $withdrawalRequest = WithdrawalRequest::where('user_id', $userId);
        $booking = Booking::where('provider_id', $userId);
        $wallet = Wallet::where('user_id', $userId)->first();
        if ($applyDateFilter) {
            $withdrawalRequest = $withdrawalRequest->whereBetween('created_at', [$startDate, $endDate]);
            $booking = $booking->whereBetween('check_out', [$startDate, $endDate]);
        }
        $total_earning = (clone $withdrawalRequest)
            ->where('status', 4)
            ->sum('amount');
        $total_cancellation = (clone $booking)
            ->where('status', 7)
            ->where('is_wallet', 0)
            ->sum('sub_total');
        $adjustments = (clone $booking)
            ->where('status', 7)
            ->where('is_wallet', 1)
            ->sum('service_refund_amount_cop');
        // $pending_withdrawl = (clone $booking)
        //     ->where('is_wallet', 0)
        //     ->sum('sub_total');
        $pending_withdrawl = (clone $booking)
            ->where('is_wallet', 0)
            ->where("status", "!=", 7)
            ->get()
            ->sum(function ($booking) {
                // Calculate amount after deducting commission, similar to CronController
                return $booking->sub_total - get_percentage($booking->listing->category->tax ?? 0, $booking->sub_total);
            });
        $in_process = (clone $withdrawalRequest)
            ->where('status', 0)
            ->sum('amount');
        $platform_fee = $wallet->commission_amount ?? 0;
        $outstanding_payments = $wallet->amount ?? 0;
        return response()->json([
            'status' => true,
            'total_earning' => $total_earning,
            'total_cancellation' => $total_cancellation,
            'adjustments' => $adjustments,
            'platform_fee' => $platform_fee,
            'outstanding_payments' => $outstanding_payments,
            'pending_withdrawl' => $pending_withdrawl,
            'in_process' => $in_process,
        ]);
    }
    public function currency($to = 'USD', $wallet_amount)
    {
        try {
            $rates =  CurrencyConversionRate::where('target_currency', 'USD')->first();
            $rate = $rates->rate ?? 1; // Fallback to 1 if rate is not found
            // session()->put('conversion_rate', $rate);
            // session()->put('currency', $to);
            return $wallet_amount * $rate;
        } catch (\Exception $e) {
            session()->put('conversion_rate', 1);
            session()->put('currency', 'COP');
        }
        return redirect()->back();
    }
    private function generateIframeUrl($payeeId)
    {
        $timestamp = time();
        $locale = app()->getLocale() ?? 'en';
        $queryString = "payer={$this->payerName}&idap={$payeeId}&ts={$timestamp}&uiculture={$locale}";
        $hashKey = hash_hmac('sha256', $queryString, $this->apiKey);
        // $iframeUrl = "http://127.0.0.1:8000/?{$queryString}&hashkey={$hashKey}";
        $iframeUrl = "https://ui2.sandbox.tipalti.com/PayeeDashboard/home?{$queryString}&hashkey={$hashKey}";
        return $iframeUrl;
    }
    public function notification()
    {
        $notifications = Notification::where("notifiable_id", auth()->id())->paginate();
        return view('dashboard.Notification', compact("notifications"));
    }
    public function delete_notifications(Request $request)
    {
        // return $request->all();
        if ($request->notifiaction) {
            foreach ($request->notifiaction as $notifiaction) {
                $noti = Notification::find($notifiaction);
                $noti->delete();
            }
            return back()->with(["type" => "success", "message" => "Notification deleted successfully", "title" => "Success"]);
        }
        return back()->with(["type" => "error", "message" => "please select message to delete", "title" => "Error"]);
    }
    function markAsRead($id)
    {
        if ($id) {
            auth()->user()->notifications->where("id", $id)->markAsRead();
        }
        return back();
    }
    function pay_withdrawal_req($id)
    {
        $withdrawal_request = WithdrawalRequest::findOrFail($id);
        if ($withdrawal_request) {
            $withdrawal_request->status = 1;
            $withdrawal_request->save();
            return back()->with(["message" => "Withdrawal Request Accepted", "type" => "success"]);
        }
    }
    public function refreshAccessToken()
    {
        $client = new Client();
        $refreshToken = CommonSetting::first()->refresh_token ?? '';
        if (!$refreshToken) {
            //return response()->json(["error" => "Refresh token is missing."], 400);
            return redirect()->route('tipalti.auth');
        }
        try {
            $response = $client->post('https://sso.sandbox.tipalti.com/connect/token', [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);
            $tokenData = json_decode($response->getBody(), true);
            session()->put('tipalti_access_token', $tokenData['access_token']);
            session()->put('tipalti_refresh_token', $tokenData['refresh_token']);
            session()->put('tipalti_token_expires_in', now()->addSeconds($tokenData['expires_in']));
            return response()->json(["message" => "Access token refreshed successfully", "token" => $tokenData]);
        } catch (\Exception $e) {
            return response()->json(["error" => "Failed to refresh access token", "message" => $e->getMessage()], 400);
        }
    }
    function mark_all_read()
    {
        auth()->user()->notifications->markAsRead();
        return back()->with("flesh_message", "Mark All Read");
    }
    function report_review_delete($id)
    {
        $report_review = ReviewReport::find($id);
        if ($report_review) {
            $report_review->delete();
            return back()->with(["message" => "Report Review Deleted", "type" => "success"]);
        } else {
            return back()->with(["message" => "Report Review Not Found", "type" => "error"]);
        }
    }
    function report_review_resolve($id)
    {
        $report_review = ReviewReport::find($id);
        if ($report_review) {
            $report_review->status = 1;
            $report_review->save();
            return redirect()->to(url()->previous() . '#reported')->with([
                "message" => "Report Resolved",
                "type" => "success"
            ]);
            // return back()->with(["message" => "Report Resolved", "type" => "success"]);
        } else {
            // return back()->with(["message" => "Report Not Resolved", "type" => "error"]);
            return redirect()->to(url()->previous() . '#reported')->with([
                "message" => "Report Not Resolved",
                "type" => "error"
            ]);
        }
    }
    function review_delete_image($id)
    {
        $review_image = ReviewImage::findOrFail($id);
        if ($review_image) {
            $this->deleteImage($review_image->image);
            $review_image->delete();
            return api_response(true, "Image deleted successfully");
            // return back()->with(["message" => "Image Deleted", "type" => "success"]);
        } else {
            return api_response(false, "Image not deleted ");
            // return back()->with(["message" => "Image Not Found", "type" => "error"]);
        }
    }
    function withdrawal_request_reject(Request $request)
    {
        try {
            $request->validate([
                "withdrawal_req_id" => "required",
                "reason" => "required",
            ]);
            $withdrawal_request = WithdrawalRequest::find($request->withdrawal_req_id);
            if ($withdrawal_request) {
                $withdrawal_request->rejected_reason = $request->reason;
                $withdrawal_request->status = 3;
                if ($withdrawal_request->save()) {
                    $wallet = Wallet::where("user_id", $withdrawal_request->user_id)->first();
                    $wallet->amount = $wallet->amount + $withdrawal_request->amount;
                    $wallet->save();
                    return back()->with(["message" => "Rejected successfully", "type" => "success"]);
                } else {
                    return back()->with(["message" => "Request Not found", "type" => "error", "title" => "Something went wrong"]);
                }
            }
        } catch (\Exception $e) {
            return back()->with(["message" => $e->getMessage(), "type" => "error", "title" => "Something went wrong"]);
        }
    }
    function user_status($id)
    {
        if (auth()->user()->hasRole(['user', "sub_admin"])) {
            $user = User::find($id);
            if ($user) {
                if ($user->approval == 0) {
                    $user->approval = 1;
                    $message = $user->name . " approved";
                } else {
                    $message = $user->name . " unapproved ";
                    $user->approval = 0;
                }
                $user->save();
                activity($message)
                    ->performedOn($user)
                    ->log('User Status update');
                return back()->with(["message" => $message, "type" => "success", "title" => "Status updated"]);
            } else {
                return back()->with(["message" => "User Not found", "type" => "error", "title" => "User not found"]);
            }
        }
    }
    function delete_user($id)
    {
        if (!in_array($id, [1, 2])) {
            $user = User::findorFail($id);
            if ($user) {
                $listings = Listing::where("user_id", $user->id)->get();
                foreach ($listings as $listing) {
                    $listing->delete();
                    $gallery_images = listingGallery::where("listing_id", $listing->id)->get();
                    foreach ($gallery_images as $value) {
                        $this->deleteImage($value->url);
                    }
                    ListingAccessibility::where("listing_id", $listing->id)->delete();
                    ListingAmenity::where("listing_id", $listing->id)->delete();
                    ListingDetail::where("listing_id", $listing->id)->delete();
                    ListingEquipment::where("listing_id", $listing->id)->delete();
                    ListingExperience::where("listing_id", $listing->id)->delete();
                    ListingExtra::where("listing_id", $listing->id)->delete();
                    listingGallery::where("listing_id", $listing->id)->delete();
                    ListingInclude::where("listing_id", $listing->id)->delete();
                    ListingItinerary::where("listing_id", $listing->id)->delete();
                    ListingKeyFeature::where("listing_id", $listing->id)->delete();
                    ListingService::where("listing_id", $listing->id)->delete();
                    Booking::where("listing_id", $listing->id)->delete();
                }
                Card::where("user_id", $user->id)->delete();
                Cart::where("user_id", $user->id)->delete();
                Report::where("user_id", $user->id)->delete();
                $user->delete();
                return api_response(true, "User deleted");
            }
            Booking::where()->delete();
            return back()->with(["message" =>  "user deleted"]);;
        } else {
            abort(403);
        }
    }
    function user_chats(Request $request, $user_id = null)
    {
        try {
            $user_chats = ChMessage::with(["from_user", "to_user"])
                ->groupBy('from_id', "to_id")
                ->get();
            $messages = [];
            $user = null;
            $to_user = null;
            if ($user_id) {
                $user = User::findOrFail(decrypt($user_id));
                $to_user = User::findOrFail(decrypt($request->to));
                $messages = ChMessage::where(function ($query) use ($user, $request) {
                    $query->where("from_id", $user->id)->where("to_id", decrypt($request->to));
                })->orWhere(function ($query) use ($user, $request) {
                    $query->where("from_id", decrypt($request->to))->where("to_id", $user->id);
                })->orderBy("created_at", "ASC")->get();
            }
            return view("dashboard.user_chats", compact('user_chats', 'messages', 'user', "to_user"));
        } catch (\Exception $e) {
            return redirect()->route("user_chats")->with(["type" => "error", "message" => $e->getMessage(), "title" => "Error"]);
        }
    }
    public function stepper_listing()
    {
        return view('dashboard.contact_cms');
    }
}
